package com.open_care.dto.huagui.rule;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import com.open_care.dto.rule.query_builder.QueryBuilderRuleExpressionDTO;
import com.open_care.validation.group.query_builder.QueryBuilderValidationGroup;
import com.open_care.validation.group.RuleValidationGroups;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 阶梯费率配置DTO
 * 规则描述必须有值
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class TieredRateConfigDTO implements IBaseDTO {
    
    @Valid
    @NotNull(message = "规则表达式不能为空", groups = {
        RuleValidationGroups.SettlementRuleGroup.class,
        QueryBuilderValidationGroup.SaveRuleGroup.class
    })
    @OcColumn(title = "规则表达式")
    private QueryBuilderRuleExpressionDTO ruleExpression;
    
    @NotBlank(message = "规则描述不能为空", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @OcColumn(title = "规则描述")
    private String ruleDescription;
    
    @NotNull(message = "费率不能为空", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @DecimalMin(value = "0.0", inclusive = false, message = "费率必须大于0", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @OcColumn(title = "费率")
    private BigDecimal rate;
    
    @NotNull(message = "最小金额不能为空", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @DecimalMin(value = "0.0", inclusive = true, message = "最小金额不能小于0", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @OcColumn(title = "最小金额")
    private BigDecimal minAmount;
    
    @OcColumn(title = "最大金额")
    private BigDecimal maxAmount;
}